# 休眠恢复功能测试指南

## 测试目的
验证应用在系统休眠/唤醒、锁屏/解锁后能否正确保存和恢复窗口状态及界面状态，同时确保应用正常退出后重新启动时不会恢复之前的状态。

## 重要说明
**修改后的行为**：
- ✅ **系统休眠/唤醒**：会自动恢复窗口和界面状态
- ✅ **锁屏/解锁**：会自动恢复窗口和界面状态
- ❌ **应用退出后重启**：不会恢复状态，从初始状态开始
- ❌ **应用崩溃后重启**：不会恢复状态，从初始状态开始

## 测试前准备

### 1. 确认修改已生效
确保以下文件的修改已经应用：
- `electron/service/os/windowState.ts` - 窗口状态管理服务
- `electron/service/os/systemEvents.ts` - 系统事件监听服务
- `electron/service/os/window.ts` - 窗口服务集成
- `electron/preload/lifecycle.ts` - 生命周期管理
- `electron/main.ts` - 主进程集成
- `frontend/src/utils/routeStateManager.ts` - 路由状态管理
- `frontend/src/stores/helper/enhancedPersist.ts` - 增强持久化
- `frontend/src/stores/modules/global.ts` - 全局状态存储
- `frontend/src/main.ts` - 前端主文件
- `frontend/src/App.vue` - 应用组件

### 2. 重新构建应用
```bash
# 安装依赖（如果需要）
npm install

# 构建前端
cd frontend
npm run build

# 构建Electron应用
cd ..
npm run build
```

## 测试步骤

### 测试1：基本窗口状态保存恢复

1. **启动应用**
   - 启动VisualDebug应用
   - 等待应用完全加载

2. **调整窗口状态**
   - 移动窗口到屏幕的特定位置
   - 调整窗口大小（例如：1600x900）
   - 最大化窗口，然后恢复到正常大小
   - 记录当前窗口的位置和大小

3. **导航到特定页面**
   - 导航到设备调试页面或其他非首页
   - 调整界面布局（如控制台高度、侧边栏状态等）
   - 记录当前页面路径和界面状态

4. **触发休眠**
   - 方法1：使用系统休眠功能（开始菜单 -> 电源 -> 休眠）
   - 方法2：合上笔记本电脑盖子（如果是笔记本）
   - 方法3：使用命令行：`rundll32.exe powrprof.dll,SetSuspendState 0,1,0`

5. **等待和唤醒**
   - 等待系统完全进入休眠状态（约10-30秒）
   - 唤醒系统（按电源键或打开笔记本盖子）
   - 等待系统完全启动

6. **验证恢复效果**
   - 检查应用是否自动启动（如果设置了自启动）
   - 检查窗口位置是否与休眠前一致
   - 检查窗口大小是否与休眠前一致
   - 检查当前页面是否与休眠前一致
   - 检查界面布局是否与休眠前一致

### 测试2：锁屏解锁测试

1. **准备状态**
   - 按照测试1的步骤2-3设置窗口和页面状态

2. **锁定屏幕**
   - 使用快捷键 `Win + L` 锁定屏幕
   - 或者等待系统自动锁屏

3. **解锁屏幕**
   - 输入密码解锁屏幕

4. **验证状态**
   - 检查应用状态是否保持不变

### 测试3：应用重启测试（验证不恢复状态）

1. **设置状态**
   - 按照测试1的步骤2-3设置状态

2. **关闭应用**
   - 正常关闭应用（不是强制结束进程）

3. **重新启动应用**
   - 重新启动VisualDebug应用

4. **验证不恢复**
   - ✅ 检查应用是否从初始状态开始（首页）
   - ✅ 检查窗口是否使用默认位置和大小
   - ✅ 检查界面布局是否为默认设置
   - ❌ 应用不应该恢复到之前的页面和状态

### 测试4：异常情况测试（验证不恢复状态）

1. **强制结束进程**
   - 设置好状态后，使用任务管理器强制结束应用进程
   - 重新启动应用，检查应用是否从初始状态开始（不恢复）

2. **系统异常重启**
   - 模拟系统异常重启（如果安全的话）
   - 检查应用是否从初始状态开始（不恢复）

### 测试5：混合场景测试

1. **休眠后正常退出**
   - 设置状态 → 系统休眠 → 唤醒（应该恢复状态）→ 正常退出应用
   - 重新启动应用，检查是否从初始状态开始（不恢复）

2. **锁屏后正常退出**
   - 设置状态 → 锁屏 → 解锁（应该恢复状态）→ 正常退出应用
   - 重新启动应用，检查是否从初始状态开始（不恢复）

## 预期结果

### 正常情况下应该看到：

1. **窗口状态恢复**
   - 窗口位置与休眠前一致
   - 窗口大小与休眠前一致
   - 最大化状态正确恢复

2. **页面状态恢复**
   - 当前页面路径与休眠前一致
   - 页面参数和查询字符串正确恢复

3. **界面布局恢复**
   - 控制台显示/隐藏状态一致
   - 控制台高度一致
   - 侧边栏状态一致
   - 其他界面元素状态一致

4. **日志输出**
   - 在开发者工具的控制台中应该能看到相关的日志输出
   - 例如：`[WindowStateService] 窗口状态已恢复`
   - 例如：`[RouteStateManager] 恢复路由状态: /device/debug`

## 故障排除

### 如果状态没有正确恢复：

1. **检查日志**
   - 打开开发者工具（F12）
   - 查看控制台是否有错误信息
   - 查看应用日志文件（logs目录）

2. **检查存储**
   - 在开发者工具的Application标签页中检查localStorage
   - 查找键名包含`visualdebug`或`simple-`的项目
   - 检查`window-state.json`文件是否存在于用户数据目录

3. **手动测试API**
   - 在开发者工具控制台中执行：
   ```javascript
   // 检查路由状态管理器
   console.log(window.routeStateManager?.getSavedState());
   
   // 手动保存状态
   window.routeStateManager?.saveRouteState();
   
   // 手动恢复状态
   window.routeStateManager?.restoreRouteState();
   ```

4. **重置状态**
   - 如果状态损坏，可以清除保存的状态：
   ```javascript
   // 清除路由状态
   localStorage.removeItem('visualdebug_route_state');
   
   // 清除全局状态
   localStorage.removeItem('simple-global');
   ```

## 测试报告模板

### 测试环境
- 操作系统：Windows 10/11
- 应用版本：
- 测试日期：

### 测试结果
- [ ] 窗口位置恢复正常
- [ ] 窗口大小恢复正常
- [ ] 页面路径恢复正常
- [ ] 界面布局恢复正常
- [ ] 锁屏解锁功能正常
- [ ] 应用重启恢复正常

### 发现的问题
1. 问题描述：
   - 重现步骤：
   - 预期结果：
   - 实际结果：
   - 错误日志：

### 建议改进
1. 改进建议：

---

**注意：** 在生产环境中测试前，请确保已经备份重要数据，并在测试环境中充分验证功能的稳定性。
