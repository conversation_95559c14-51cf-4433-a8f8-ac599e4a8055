# 休眠恢复功能修复说明

## 问题描述
在系统休眠后重新开机，VisualDebug应用界面会自动刷新并切回到重新打开的初始界面，丢失用户之前的工作状态。

## 解决方案概述
通过实现智能的状态管理系统，**区分应用退出和系统休眠场景**：
- ✅ **系统休眠/唤醒、锁屏/解锁**：自动保存和恢复窗口状态、路由状态和用户界面状态
- ❌ **应用正常退出后重启**：不恢复状态，从初始状态开始，避免意外的状态恢复

## 核心设计理念
**只在非主观退出的情况下恢复状态**：
- 用户主动关闭应用 → 不恢复（符合用户预期）
- 系统休眠/锁屏 → 恢复（保持工作连续性）
- 应用崩溃 → 不恢复（避免恢复到错误状态）

## 修复内容

### 1. Electron主进程修改

#### 新增文件：
- `electron/service/os/windowState.ts` - 窗口状态管理服务
- `electron/service/os/systemEvents.ts` - 系统事件监听服务

#### 修改文件：
- `electron/service/os/window.ts` - 集成窗口状态管理
- `electron/preload/lifecycle.ts` - 添加状态恢复逻辑
- `electron/main.ts` - 集成系统事件服务
- `electron/controller/common/window.ts` - 添加状态管理API

#### 主要功能：
- 监听系统休眠/唤醒事件
- 监听锁屏/解锁事件
- 自动保存窗口位置、大小、最大化状态
- 在适当时机恢复窗口状态
- 定时保存状态（防止意外丢失）

### 2. 前端Vue应用修改

#### 新增文件：
- `frontend/src/utils/routeStateManager.ts` - 路由状态管理器
- `frontend/src/stores/helper/enhancedPersist.ts` - 增强持久化配置
- `frontend/src/utils/stateDebugger.ts` - 状态调试工具

#### 修改文件：
- `frontend/src/main.ts` - 集成路由状态管理
- `frontend/src/App.vue` - 优化状态恢复逻辑
- `frontend/src/stores/modules/global.ts` - 使用增强持久化

#### 主要功能：
- 自动保存当前路由和页面状态
- 监听页面可见性变化
- 多重备份存储策略
- 状态完整性验证和修复
- 开发调试工具

## 核心特性

### 1. 窗口状态管理
- ✅ 窗口位置保存与恢复
- ✅ 窗口大小保存与恢复
- ✅ 最大化/最小化状态保存
- ✅ 全屏状态保存
- ✅ 多显示器支持

### 2. 路由状态管理
- ✅ 当前页面路径保存
- ✅ 路由参数和查询字符串保存
- ✅ 页面状态保存（如表单数据）
- ✅ 智能恢复逻辑

### 3. 界面状态管理
- ✅ 控制台显示/隐藏状态
- ✅ 控制台高度设置
- ✅ 侧边栏展开/收起状态
- ✅ 主题和语言设置
- ✅ 布局模式设置

### 4. 系统事件监听
- ✅ 系统休眠事件
- ✅ 系统唤醒事件
- ✅ 屏幕锁定/解锁事件
- ✅ 应用焦点变化事件
- ✅ 窗口最小化/恢复事件

### 5. 数据持久化
- ✅ 多重备份存储
- ✅ 数据完整性验证
- ✅ 自动修复损坏数据
- ✅ 过期数据清理

## 🎯 解决的问题

### 修复后的行为对比

| 场景 | 修复前 | 修复后 |
|------|--------|--------|
| 系统休眠/唤醒 | ❌ 界面刷新，回到初始状态 | ✅ 正确恢复到休眠前状态 |
| 锁屏/解锁 | ❌ 可能丢失状态 | ✅ 保持当前状态不变 |
| 应用正常退出后重启 | ❌ 可能意外恢复状态 | ✅ 从初始状态开始（符合预期） |
| 应用崩溃后重启 | ❌ 可能恢复到错误状态 | ✅ 从初始状态开始（避免错误） |

### 核心改进
- ✅ **智能状态识别**：区分主动退出和被动中断
- ✅ **精确恢复时机**：只在合适的场景下恢复状态
- ✅ **用户体验优化**：符合用户对应用行为的预期
- ✅ **数据安全性**：避免恢复到损坏或错误的状态

## 使用方法

### 1. 正常使用
修复后的应用会自动处理状态保存和恢复，用户无需进行任何额外操作。

### 2. 开发调试
在开发环境下，可以使用内置的调试工具：

```javascript
// 在浏览器控制台中使用以下命令

// 查看帮助
stateDebugger.help()

// 显示所有状态
stateDebugger.showAllStates()

// 手动保存状态
stateDebugger.saveAllStates()

// 手动恢复状态
stateDebugger.restoreAllStates()

// 模拟休眠恢复测试
stateDebugger.simulateSuspendResume()

// 检查状态完整性
stateDebugger.checkStateIntegrity()

// 清除所有状态（重置应用）
stateDebugger.clearAllStates()

// 导出状态数据
stateDebugger.exportStates()
```

### 3. 手动控制
如果需要手动控制状态保存和恢复，可以通过以下API：

```javascript
// 前端路由状态管理
import { routeStateManager } from '@/utils/routeStateManager';

// 保存当前路由状态
routeStateManager.saveRouteState();

// 恢复路由状态
routeStateManager.restoreRouteState();

// 清除保存的状态
routeStateManager.clearSavedState();
```

## 配置选项

### 1. 窗口状态管理配置
在 `electron/service/os/windowState.ts` 中可以调整：
- 状态保存间隔（默认30秒）
- 状态过期时间（默认7天）
- 状态文件位置

### 2. 路由状态管理配置
在 `frontend/src/utils/routeStateManager.ts` 中可以调整：
- 状态保存间隔（默认30秒）
- 状态过期时间（默认24小时）
- 监听的事件类型

### 3. 持久化配置
在 `frontend/src/stores/helper/enhancedPersist.ts` 中可以调整：
- 存储策略（localStorage/sessionStorage）
- 备份策略
- 序列化方式

## 测试指南

详细的测试步骤请参考 `test-suspend-resume.md` 文件。

### 快速测试步骤：
1. 启动应用并导航到特定页面
2. 调整窗口大小和位置
3. 使用系统休眠功能或锁屏
4. 唤醒系统或解锁屏幕
5. 验证应用状态是否正确恢复

## 故障排除

### 常见问题：

1. **状态没有保存**
   - 检查用户数据目录权限
   - 查看控制台错误信息
   - 确认localStorage可用

2. **状态没有恢复**
   - 检查状态文件是否存在
   - 查看应用日志
   - 使用调试工具检查状态完整性

3. **窗口位置不正确**
   - 可能是多显示器配置变化
   - 检查显示器分辨率设置
   - 手动重置窗口状态

### 日志位置：
- 应用日志：`logs/` 目录
- 窗口状态文件：用户数据目录下的 `window-state.json`
- 浏览器控制台：开发者工具 -> Console

## 技术细节

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐
│   Electron      │    │   Vue Frontend  │
│   Main Process  │    │   Application   │
├─────────────────┤    ├─────────────────┤
│ WindowState     │◄──►│ RouteState      │
│ SystemEvents    │    │ GlobalState     │
│ Lifecycle       │    │ StateDebugger   │
└─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ window-state.   │    │ localStorage/   │
│ json            │    │ sessionStorage  │
└─────────────────┘    └─────────────────┘
```

### 状态同步流程
1. 用户操作触发状态变化
2. 状态管理器自动保存状态
3. 系统事件触发保存/恢复
4. 应用启动时自动恢复状态
5. 定时备份确保数据安全

## 版本兼容性
- Electron: 支持所有现代版本
- Vue 3: 完全兼容
- TypeScript: 完全支持
- Windows: 完全支持
- macOS: 基本支持（部分系统事件可能不同）
- Linux: 基本支持（部分系统事件可能不同）

## 更新日志
- v1.0.0: 初始实现，支持基本的窗口和路由状态管理
- v1.1.0: 添加系统事件监听和增强持久化
- v1.2.0: 添加状态调试工具和完整性验证

## 贡献指南
如果发现问题或有改进建议，请：
1. 查看现有的issue
2. 提供详细的重现步骤
3. 包含相关的日志信息
4. 描述预期和实际行为

---

**注意：** 此修复涉及多个核心文件的修改，建议在测试环境中充分验证后再部署到生产环境。
